<template>
  <div class="space-y-6">
    <div>
      <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">默认推送内容</h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">此选项控制点击工具栏图标时默认推送的内容。</p>
      
      <div class="space-y-3">
        <div class="radio-group">
          <input 
            type="radio" 
            id="clipboard-basic" 
            v-model="localSettings.default_push_content" 
            value="clipboard"
            class="radio-input"
            @change="handleChange"
          >
          <label for="clipboard-basic" class="text-gray-700 dark:text-gray-300">剪贴板内容</label>
        </div>

        <div class="radio-group">
          <input
            type="radio"
            id="URL-basic"
            v-model="localSettings.default_push_content"
            value="URL"
            class="radio-input"
            @change="handleChange"
          >
          <label for="URL-basic" class="text-gray-700 dark:text-gray-300">当前页面 URL</label>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { Settings } from '@/types/index'

interface Props {
  settings: Settings
}

interface Emits {
  (e: 'update:settings', settings: Settings): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const localSettings = ref<Settings>({ ...props.settings })

// 监听 props 变化
watch(() => props.settings, (newSettings) => {
  localSettings.value = { ...newSettings }
}, { deep: true })

// 处理变化
const handleChange = () => {
  emit('update:settings', { ...localSettings.value })
}
</script>
