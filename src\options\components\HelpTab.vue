<template>
  <div class="space-y-6">
    <div>
      <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">使用提示</h3>
      <ul class="space-y-2 text-gray-700 dark:text-gray-300">
        <li class="flex items-start">
          <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
          如果您设置了多个地址，请使用右键菜单将消息推送到指定设备
        </li>
        <li class="flex items-start">
          <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
          点击扩展图标可以快速推送剪贴板内容或当前页面 URL
        </li>
        <li class="flex items-start">
          <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
          选中文本后右键可以直接推送选中的内容
        </li>
        <li class="flex items-start">
          <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
          在推送参数中可以配置通知级别、铃声、图标等高级选项
        </li>
        <li class="flex items-start">
          <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
          支持加密推送，需要在 Bark 应用中配置相应的解密密钥
        </li>
      </ul>
    </div>

    <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
      <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">常见问题</h3>
      <div class="space-y-4">
        <div>
          <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Q: 如何获取 Bark 推送地址？</h4>
          <p class="text-gray-600 dark:text-gray-400 text-sm">
            A: 在 iPhone 上安装 Bark 应用，打开后会显示您的推送地址，格式类似：https://day.app/your_key/
          </p>
        </div>
        
        <div>
          <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Q: 为什么推送失败？</h4>
          <p class="text-gray-600 dark:text-gray-400 text-sm">
            A: 请检查网络连接、推送地址是否正确，以及 Bark 应用是否正常运行。可以使用测试功能验证设备配置。
          </p>
        </div>
        
        <div>
          <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Q: 如何在特殊页面使用剪贴板推送？</h4>
          <p class="text-gray-600 dark:text-gray-400 text-sm">
            A: 由于浏览器安全限制，在 chrome:// 等特殊页面无法读取剪贴板。请在普通网页上使用此功能。
          </p>
        </div>
      </div>
    </div>

    <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
      <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">关于</h3>
      <div class="text-gray-600 dark:text-gray-400 text-sm space-y-2">
        <p>Bark Chrome Extension v2.0.0</p>
        <p>基于 Vue 3.5 和 Manifest V3 构建</p>
        <p>支持 TypeScript 和现代化开发工具链</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这个组件是纯展示组件，不需要额外的逻辑
</script>
