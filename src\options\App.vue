<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <!-- 头部 -->
      <div class="card mb-8">
        <div class="flex items-center mb-4">
          <img src="/assets/bark_48.png" alt="Bark" class="w-12 h-12 mr-4">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Bark Extension 设置</h1>
            <p class="text-gray-600 dark:text-gray-400">配置您的推送设备和偏好设置</p>
          </div>
        </div>
      </div>

      <!-- Toast 通知组件 -->
      <ToastNotification ref="toastRef" />

      <!-- Tab 导航和内容 -->
      <div class="card">
        <!-- Tab 导航 -->
        <div class="border-b border-gray-200 dark:border-gray-700">
          <nav class="-mb-px flex space-x-8">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="activeTab = tab.id"
              class="py-4 px-1 border-b-2 font-medium text-sm transition-colors whitespace-nowrap"
              :class="activeTab === tab.id
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'"
            >
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path v-if="tab.id === 'basic'" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  <path v-else-if="tab.id === 'params'" d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                  <path v-else-if="tab.id === 'devices'" d="M17,8C8,10 5.9,16.17 3.82,21.34l1.89,.66 .07-.22C8.84,17.23 12.6,12.31 17,8l3,3h2V3h-8L17,8z"/>
                  <path v-else d="M12,2C6.48,2 2,6.48 2,12s4.48,10 10,10 10,-4.48 10,-10S17.52,2 12,2zM13,17h-2v-6h2v6zM13,9h-2V7h2v2z"/>
                </svg>
                {{ tab.name }}
              </div>
            </button>
          </nav>
        </div>

        <!-- Tab 内容 -->
        <div class="mt-6">
          <!-- 基本设置 Tab -->
          <div v-show="activeTab === 'basic'">
            <BasicSettings
              :settings="settings"
              @update:settings="handleSettingsUpdate"
            />
          </div>

          <!-- 推送参数 Tab -->
          <div v-show="activeTab === 'params'">
            <PushParams
              :settings="settings"
              @update:settings="handleSettingsUpdate"
              @reset-params="handleResetParams"
            />
          </div>

          <!-- 设备管理 Tab -->
          <div v-show="activeTab === 'devices'">
            <DeviceManagement
              :settings="settings"
              @update:settings="handleSettingsUpdate"
              @show-message="showStatus"
            />
          </div>

          <!-- 使用帮助 Tab -->
          <div v-show="activeTab === 'help'">
            <HelpTab />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { storage, defaultSettings } from '@/utils/storage'
import { PushService } from '@/utils/push'
import { validateURL } from '@/utils/index'
import ToastNotification from './components/ToastNotification.vue'
import BasicSettings from './components/BasicSettings.vue'
import PushParams from './components/PushParams.vue'
import DeviceManagement from './components/DeviceManagement.vue'
import HelpTab from './components/HelpTab.vue'
const pushService = new PushService();

// Tab 导航系统
const activeTab = ref('basic')
const tabs = ref([
  { id: 'basic', name: '基本设置' },
  { id: 'params', name: '推送参数' },
  { id: 'devices', name: '设备管理' },
  { id: 'help', name: '使用帮助' }
])

// Toast 通知组件引用
const toastRef = ref(null)

// 设置数据
const settings = ref({
  server_urls: [],
  default_push_content: 'clipboard',
  push_params: {
    title: "",
    subtitle: "",
    level: "active",
    volume: 5,
    badge: "",
    call: false,
    autoCopy: false,
    sound: "",
    icon: "",
    group: "",
    ciphertext: ""
  }
})



// 加载设置
const loadSettings = async () => {
  try {
    // 获取所有已保存的数据
    const savedSettings = await storage.get(null) // null 表示获取所有数据

    console.log('Loaded raw data:', savedSettings)
    console.log('savedSettings.server_urls:', savedSettings.server_urls)
    console.log('Is array:', Array.isArray(savedSettings.server_urls))
    console.log('Type:', typeof savedSettings.server_urls)

    // 确保 server_urls 是数组，并保持响应式
    let loadedServerUrls = defaultSettings.server_urls

    if (savedSettings.server_urls) {
      if (Array.isArray(savedSettings.server_urls)) {
        loadedServerUrls = savedSettings.server_urls
      } else {
        // 如果是对象，尝试转换为数组
        console.warn('server_urls is not an array, attempting to convert:', savedSettings.server_urls)
        const urlsArray = Object.values(savedSettings.server_urls)
        if (urlsArray.length > 0 && urlsArray[0].server_name && urlsArray[0].server_url) {
          loadedServerUrls = urlsArray
          console.log('Converted to array:', loadedServerUrls)
        }
      }
    }

    console.log('Final loadedServerUrls:', loadedServerUrls)

    // 清空现有数组并添加新数据，确保响应式
    settings.value.server_urls.splice(0, settings.value.server_urls.length, ...loadedServerUrls)

    console.log('Updated settings.value.server_urls:', settings.value.server_urls)
    console.log('Is array after update:', Array.isArray(settings.value.server_urls))
    settings.value.default_push_content = savedSettings.default_push_content ?? defaultSettings.default_push_content

    // 加载推送参数，合并默认值
    settings.value.push_params = {
      ...defaultSettings.push_params,
      ...(savedSettings.push_params || {})
    }

    console.log('Loaded push_params:', settings.value.push_params)
  } catch (error) {
    console.error('Load settings failed:', error)
    // 如果加载失败，重置为默认设置
    settings.value.server_urls = []
    settings.value.default_push_content = defaultSettings.default_push_content
    settings.value.push_params = { ...defaultSettings.push_params }
    showStatus('加载设置失败，使用默认设置', 'error')
  }
}

// 保存设置
const saveSettings = async () => {
  try {
    // 确保数据结构正确
    const dataToSave = {
      server_urls: Array.isArray(settings.value.server_urls) ? [...settings.value.server_urls] : [],
      default_push_content: settings.value.default_push_content || 'clipboard',
      push_params: { ...settings.value.push_params }
    }

    console.log('Saving data:', dataToSave)
    console.log('server_urls is array:', Array.isArray(dataToSave.server_urls))

    await storage.set(dataToSave)
    console.log('Settings saved successfully')
    showStatus('保存成功', 'success')
  } catch (error) {
    console.error('Save settings failed:', error)
    showStatus('保存设置失败', 'error')
  }
}

// 重置推送参数
const resetPushParams = async () => {
  try {
    settings.value.push_params = { ...defaultSettings.push_params }
    await saveSettings()
    showStatus('推送参数已重置为默认值', 'success')
  } catch (error) {
    console.error('Reset push params failed:', error)
    showStatus('重置推送参数失败', 'error')
  }
}

// Toast 通知系统函数
const showStatus = (message, type = 'success') => {
  if (toastRef.value) {
    toastRef.value.showToast(message, type)
  }
}

// 处理设置更新
const handleSettingsUpdate = async (newSettings) => {
  settings.value = { ...newSettings }
  await saveSettings()
}

// 处理推送参数重置
const handleResetParams = async () => {
  try {
    settings.value.push_params = { ...defaultSettings.push_params }
    await saveSettings()
    showStatus('推送参数已重置为默认值', 'success')
  } catch (error) {
    console.error('Reset push params failed:', error)
    showStatus('重置推送参数失败', 'error')
  }
}

// 添加服务器
const addServer = async () => {
  if (!canAddServer.value) return

  // 验证 URL
  if (!validateURL(newDevice.value.url)) {
    showStatus('请输入有效的 URL', 'error')
    return
  }

  const newServer = {
    server_name: newDevice.value.name.trim(),
    server_url: newDevice.value.url.trim()
  }

  // 确保 server_urls 是数组
  if (!Array.isArray(settings.value.server_urls)) {
    settings.value.server_urls = []
  }

  settings.value.server_urls.push(newServer)
  await saveSettings()

  // 清空表单
  newDevice.value.name = ''
  newDevice.value.url = ''

  showStatus('设备添加成功', 'success')
}

// 删除服务器
const deleteServer = async (index) => {
  if (confirm('确定要删除这个设备吗？')) {
    // 确保 server_urls 是数组
    if (!Array.isArray(settings.value.server_urls)) {
      settings.value.server_urls = []
      return
    }

    settings.value.server_urls.splice(index, 1)
    await saveSettings()
    showStatus('设备已删除', 'success')
  }
}

// 测试设备推送功能
const testDevice = async (index) => {
  const server = settings.value.server_urls[index]
  if (!server) return

  // 设置测试状态
  testingStates.value[index] = true
  testResults.value[index] = null

  try {
    const testMessage = `测试消息 - ${new Date().toLocaleTimeString()}`

    // 直接发送 HTTP 请求
    const serverUrl = server.server_url

    pushService.sendMessage(testMessage, {serverUrl});

    // 测试成功
    testResults.value[index] = {
      success: true,
      message: '测试成功！请检查设备是否收到通知'
    }

  } catch (error) {
    // 测试失败
    testResults.value[index] = {
      success: false,
      message: `测试失败：${error.message || '网络错误或设备配置有误'}`
    }
  } finally {
    // 清除测试状态
    testingStates.value[index] = false

    // 5秒后清除测试结果
    setTimeout(() => {
      if (testResults.value[index]) {
        testResults.value[index] = null
      }
    }, 5000)
  }
}

// 测试全部设备
const testAllDevices = async () => {
  if (!Array.isArray(settings.value.server_urls) || settings.value.server_urls.length === 0) {
    showStatus('没有可测试的设备', 'error')
    return
  }

  isTestingAll.value = true

  try {
    // 并行测试所有设备
    const testPromises = settings.value.server_urls.map((_, index) => testDevice(index))
    await Promise.allSettled(testPromises)

    // 统计测试结果
    const successCount = Object.values(testResults.value).filter(result => result?.success).length
    const totalCount = settings.value.server_urls.length

    if (successCount === totalCount) {
      showStatus(`全部 ${totalCount} 个设备测试成功`, 'success')
    } else {
      showStatus(`${successCount}/${totalCount} 个设备测试成功`, 'error')
    }
  } finally {
    isTestingAll.value = false
  }
}

// 页面加载时初始化
onMounted(() => {
  loadSettings()
})
</script>


