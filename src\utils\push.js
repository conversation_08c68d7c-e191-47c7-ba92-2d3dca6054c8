import { storage } from "./storage.js";
import type { Settings, PushOptions, PushParams } from '../types/index.js';

// 推送消息工具函数
export class PushService {
  constructor() {
    // 移除无用的属性
  }

  async sendMessage(content: string, options: PushOptions = {}): Promise<void> {
    try {
      const settings = await storage.get<Settings>(null);
      const pushParams: PushParams = settings.push_params || {};
      const serverUrl = options.serverUrl || settings.server_urls[0]?.server_url;

      if (!serverUrl) {
        throw new Error('未配置推送服务器');
      }
  
      // 构建推送参数，合并用户配置和默认值
      const param = {
        body: content
      };
  
      // 添加基本参数
      if (pushParams.title && pushParams.title.trim() !== '') {
        param.title = pushParams.title;
      } else {
        param.title = 'Chrome 通知';
      }
  
      if (pushParams.subtitle && pushParams.subtitle.trim() !== '') {
        param.subtitle = pushParams.subtitle;
      }
  
      if (pushParams.group && pushParams.group.trim() !== '') {
        param.group = pushParams.group;
      } else {
        param.group = 'Chrome 通知';
      }
  
      // 添加推送级别
      if (pushParams.level && pushParams.level !== 'active') {
        param.level = pushParams.level;
      }
  
      // 添加音量设置（仅在 critical 级别下有效）
      if (pushParams.level === 'critical' && pushParams.volume !== undefined && pushParams.volume !== 5) {
        param.volume = pushParams.volume;
      }
  
      // 添加角标
      if (pushParams.badge && pushParams.badge !== '') {
        param.badge = parseInt(pushParams.badge) || 0;
      }
  
      // 添加重复播放铃声
      if (pushParams.call) {
        param.call = "1";
      }
  
      // 添加自动复制设置
      if (pushParams.autoCopy) {
        param.autoCopy = "1";
      }
  
      // 添加自定义复制内容
      if (pushParams.copy && pushParams.copy.trim() !== '') {
        param.copy = pushParams.copy;
      }
  
      // 添加自定义铃声
      if (pushParams.sound && pushParams.sound.trim() !== '') {
        param.sound = pushParams.sound;
      }
  
      // 添加自定义图标
      if (pushParams.icon && pushParams.icon.trim() !== '') {
        param.icon = pushParams.icon;
      }
  
      // 添加加密密文
      if (pushParams.ciphertext && pushParams.ciphertext.trim() !== '') {
        param.ciphertext = pushParams.ciphertext;
      }
  
      console.log('Sending message with params:', param);
  
      // 发送 POST 请求
      const response = await fetch(serverUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(param)
      });
  
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
  
      const result = await response.text();
      if (result === 'error') {
        throw new Error('服务器返回错误');
      }
  
      console.log('Message sent successfully');
    } catch (error) {
      console.error('Error processing URL:', error);
      throw error; // 重新抛出错误以便上层处理
    }
  }

}
