// Content Script for getting page selection

interface ContentMessage {
  method?: string;
}

interface ContentResponse {
  data?: string;
}

chrome.runtime.onMessage.addListener((
  request: ContentMessage,
  sender: chrome.runtime.MessageSender,
  sendResponse: (response: ContentResponse) => void
): void => {
  console.log('Content script received message:', request);

  if (request.method === "getSelection") {
    const selection = window.getSelection();
    const selectedText = selection ? selection.toString() : '';
    sendResponse({ data: selectedText });
  } else {
    sendResponse({});
  }
});

// 可以在这里添加更多页面交互功能
console.log('Bark Chrome Extension content script loaded');
