<template>
  <div class="space-y-6">
    <div>
      <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">推送参数配置</h3>
      <p class="text-gray-600 dark:text-gray-400 mb-6">配置 Bark 推送的高级参数，这些参数将应用于所有推送消息。</p>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 基本参数 -->
        <div class="space-y-4">
          <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">基本参数</h4>
          
          <!-- 推送标题 -->
          <div>
            <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              推送标题
            </label>
            <input
              type="text"
              id="title"
              v-model="localSettings.push_params.title"
              @input="handleChange"
              placeholder="自定义推送标题（留空使用默认）"
              class="input-field"
            >
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">留空将使用默认标题</p>
          </div>

          <!-- 推送副标题 -->
          <div>
            <label for="subtitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              推送副标题
            </label>
            <input
              type="text"
              id="subtitle"
              v-model="localSettings.push_params.subtitle"
              @input="handleChange"
              placeholder="推送副标题（可选）"
              class="input-field"
            >
          </div>

          <!-- 推送级别 -->
          <div>
            <label for="level" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              推送中断级别
            </label>
            <select
              id="level"
              v-model="localSettings.push_params.level"
              @change="handleChange"
              class="input-field"
            >
              <option value="active">默认 (active) - 立即亮屏显示</option>
              <option value="critical">重要警告 (critical) - 静音模式下也响铃</option>
              <option value="timeSensitive">时效性 (timeSensitive) - 专注状态下显示</option>
              <option value="passive">被动 (passive) - 仅添加到通知列表</option>
            </select>
          </div>

          <!-- 通知音量 -->
          <div>
            <label for="volume" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              通知音量 (0-10)
            </label>
            <div class="flex items-center space-x-3">
              <input
                type="range"
                id="volume"
                v-model.number="localSettings.push_params.volume"
                @input="handleChange"
                min="0"
                max="10"
                class="flex-1"
              >
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-8">
                {{ localSettings.push_params.volume }}
              </span>
            </div>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">仅在重要警告级别下生效</p>
          </div>
        </div>

        <!-- 高级参数 -->
        <div class="space-y-4">
          <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">高级参数</h4>

          <!-- 推送角标 -->
          <div>
            <label for="badge" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              推送角标
            </label>
            <input
              type="number"
              id="badge"
              v-model.number="localSettings.push_params.badge"
              @input="handleChange"
              placeholder="角标数字（可选）"
              class="input-field"
              min="0"
            >
          </div>
          
          <!-- 自定义铃声 -->
          <div>
            <label for="sound" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              自定义铃声
            </label>
            <select
              id="sound"
              v-model="localSettings.push_params.sound"
              @change="handleChange"
              class="input-field"
            >
              <option value="">默认铃声</option>
              <option v-for="sound in soundOptions" :key="sound" :value="sound">
                {{ sound }}
              </option>
            </select>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">选择预设铃声或使用默认</p>
          </div>

          <!-- 自动复制推送内容 -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="autoCopyParam"
              v-model="localSettings.push_params.autoCopy"
              @change="handleChange"
              class="checkbox-input"
            >
            <label for="autoCopyParam" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
              自动复制推送内容到设备剪贴板
            </label>
          </div>

          <!-- 重复播放铃声 -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="call"
              v-model="localSettings.push_params.call"
              @change="handleChange"
              class="checkbox-input"
            >
            <label for="call" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
              重复播放铃声
            </label>
          </div>
        </div>
      </div>

      <!-- 更多高级参数 -->
      <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">更多选项</h4>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 自定义图标 -->
          <div>
            <label for="icon" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              自定义图标 URL
            </label>
            <input
              type="url"
              id="icon"
              v-model="localSettings.push_params.icon"
              @input="handleChange"
              placeholder="https://example.com/icon.png"
              class="input-field"
            >
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">图标会自动缓存，相同 URL 仅下载一次</p>
          </div>

          <!-- 消息分组 -->
          <div>
            <label for="group" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              消息分组
            </label>
            <input
              type="text"
              id="group"
              v-model="localSettings.push_params.group"
              @input="handleChange"
              placeholder="分组名称（可选）"
              class="input-field"
            >
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">相同分组的消息会聚合显示</p>
          </div>

          <!-- 加密密文 -->
          <div class="md:col-span-2">
            <label for="ciphertext" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              加密推送密文
            </label>
            <textarea
              id="ciphertext"
              v-model="localSettings.push_params.ciphertext"
              @input="handleChange"
              placeholder="加密推送的密文（高级功能）"
              rows="3"
              class="input-field resize-none"
            ></textarea>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">用于加密推送，需要在 Bark 应用中配置相应的解密密钥</p>
          </div>
        </div>
      </div>

      <!-- 重置按钮 -->
      <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          @click="resetParams"
          class="btn-secondary"
        >
          重置推送参数
        </button>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">将所有推送参数重置为默认值</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { Settings } from '@/types/index'
import { defaultSettings } from '@/utils/storage'

interface Props {
  settings: Settings
}

interface Emits {
  (e: 'update:settings', settings: Settings): void
  (e: 'reset-params'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const localSettings = ref<Settings>({ ...props.settings })

// 铃声选项
const soundOptions = [
  'alarm', 'anticipate', 'bell', 'birdsong', 'bloom', 'calypso', 'chime', 'choo',
  'descent', 'electronic', 'fanfare', 'glass', 'gotosleep', 'healthnotification',
  'horn', 'ladder', 'mailsent', 'minuet', 'multiwayinvitation', 'newmail',
  'newsflash', 'noir', 'paymentsuccess', 'shake', 'sherwoodforest', 'spell',
  'suspense', 'telegraph', 'tiptoes', 'typewriters', 'update'
]

// 监听 props 变化
watch(() => props.settings, (newSettings) => {
  localSettings.value = { ...newSettings }
}, { deep: true })

// 处理变化
const handleChange = () => {
  emit('update:settings', { ...localSettings.value })
}

// 重置参数
const resetParams = () => {
  localSettings.value.push_params = { ...defaultSettings.push_params }
  emit('reset-params')
}
</script>
