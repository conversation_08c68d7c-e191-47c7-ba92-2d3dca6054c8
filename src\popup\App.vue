<template>
  <div class="p-4 bg-white dark:bg-gray-800">
    <div class="flex items-center mb-4">
      <img src="/assets/bark_48.png" alt="Bark" class="w-8 h-8 mr-2">
      <h1 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Bark 推送</h1>
    </div>

    <div class="space-y-3">
      <!-- 快速推送按钮 -->
      <button 
        @click="pushClipboard"
        :disabled="loading"
        class="btn-primary w-full"
      >
        <span v-if="loading">推送中...</span>
        <span v-else>推送剪贴板</span>
      </button>

      <button 
        @click="pushCurrentUrl"
        :disabled="loading"
        class="btn-secondary w-full"
      >
        推送当前页面
      </button>

      <!-- 服务器状态 -->
      <div v-if="servers.length > 0" class="text-sm text-gray-600 dark:text-gray-400">
        <div class="flex items-center">
          <div class="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full mr-2"></div>
          已配置 {{ servers.length }} 个设备
        </div>
      </div>

      <div v-else class="text-sm text-red-600 dark:text-red-400">
        <div class="flex items-center">
          <div class="w-2 h-2 bg-red-500 dark:bg-red-400 rounded-full mr-2"></div>
          未配置设备
        </div>
      </div>

      <!-- 设置按钮 -->
      <button 
        @click="openOptions"
        class="btn-secondary w-full text-sm"
      >
        打开设置
      </button>
    </div>

    <!-- Toast 通知 -->
    <div
      v-for="toast in toasts"
      :key="toast.id"
      class="fixed top-2 left-2 right-2 z-50 transform transition-all duration-300 ease-in-out"
      :class="toast.visible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-50'"
    >
      <div
        class="flex items-center p-3 rounded-lg shadow-lg border text-sm"
        :class="getToastClass(toast.type)"
      >
        <!-- 图标 -->
        <div class="flex-shrink-0 mr-2">
          <svg
            v-if="toast.type === 'success'"
            class="w-4 h-4 text-green-600 dark:text-green-400"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          <svg
            v-else
            class="w-4 h-4 text-red-600 dark:text-red-400"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
          </svg>
        </div>

        <!-- 消息内容 -->
        <div class="flex-1 font-medium">
          {{ toast.message }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, nextTick } from 'vue'
import { storage } from '@/utils/storage'

export default {
  name: 'PopupApp',
  setup() {
    const loading = ref(false)
    const servers = ref([])

    // Toast 通知系统
    const toasts = ref([])
    let toastIdCounter = 0

    // 加载设置
    const loadSettings = async () => {
      const settings = await storage.get(null)
      servers.value = settings.server_urls || []
    }

    // 推送剪贴板内容
    const pushClipboard = async () => {
      if (servers.value.length === 0) {
        showMessage('请先在设置中配置服务器', 'error')
        return
      }

      loading.value = true
      try {
        console.log('Sending pushClipboard message to background script...')
        // 发送消息给 background script 并等待响应
        const response = await chrome.runtime.sendMessage({
          action: 'pushClipboard'
        })

        console.log('Background script response:', response)

        if (response && response.success) {
          showMessage('推送成功', 'success')
        } else {
          const errorMsg = response?.error || '推送失败，请检查设置'
          console.error('Push failed with response:', response)
          showMessage(errorMsg, 'error')
        }
      } catch (error) {
        console.error('Push failed:', error)
        showMessage('推送失败: ' + error.message, 'error')
      } finally {
        loading.value = false
      }
    }

    // 推送当前页面 URL
    const pushCurrentUrl = async () => {
      if (servers.value.length === 0) {
        showMessage('请先在设置中配置服务器', 'error')
        return
      }

      loading.value = true
      try {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
        if (tabs.length > 0) {
          await chrome.runtime.sendMessage({
            action: 'pushUrl',
            url: tabs[0].url
          })
          showMessage('推送成功', 'success')
        }
      } catch (error) {
        console.error('Push failed:', error)
        showMessage('推送失败', 'error')
      } finally {
        loading.value = false
      }
    }

    // 打开设置页面
    const openOptions = () => {
      chrome.tabs.create({ url: 'options.html' })
      window.close()
    }

    // 显示 Toast 消息
    const showMessage = (message, type = 'success') => {
      const id = ++toastIdCounter
      const toast = {
        id,
        message,
        type,
        visible: false
      }

      toasts.value.push(toast)

      // 延迟显示动画
      nextTick(() => {
        toast.visible = true
      })

      // 自动移除
      setTimeout(() => {
        removeToast(id)
      }, 4000)
    }

    // 移除 Toast
    const removeToast = (id) => {
      const index = toasts.value.findIndex(toast => toast.id === id)
      if (index > -1) {
        toasts.value[index].visible = false
        setTimeout(() => {
          toasts.value.splice(index, 1)
        }, 300) // 等待动画完成
      }
    }

    // 获取 Toast 样式类
    const getToastClass = (type) => {
      return type === 'success'
        ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-700'
        : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 border border-red-200 dark:border-red-700'
    }

    onMounted(() => {
      loadSettings()
    })

    return {
      loading,
      servers,
      toasts,
      pushClipboard,
      pushCurrentUrl,
      openOptions,
      getToastClass
    }
  }
}
</script>
