# Bark Chrome Extension V3

这是使用 Manifest V3、Vue 3 和 Tailwind CSS 重写的 Bark Chrome 扩展。

## 主要改进

### 技术栈升级
- ✅ **Manifest V3**: 升级到最新的 Chrome Extension 标准
- ✅ **Vue 3**: 使用现代化的 Vue 3 框架（可选，也提供纯 HTML 版本）
- ✅ **Tailwind CSS**: 现代化的 CSS 框架，提供美观的界面
- ✅ **ES6+**: 使用现代 JavaScript 语法
- ✅ **Vite**: 现代化的构建工具

### 功能改进
- ✅ **现代化界面**: 使用 Tailwind CSS 重新设计的美观界面
- ✅ **更好的用户体验**: 改进的设置页面和弹出窗口
- ✅ **Service Worker**: 适配 Manifest V3 的 Service Worker 模式
- ✅ **权限优化**: 更精确的权限控制
- ✅ **错误处理**: 更好的错误处理和用户反馈

## 项目结构

```
├── src/                    # 源代码目录
│   ├── background/         # Background Script (Service Worker)
│   ├── content/           # Content Script
│   ├── popup/             # 弹出窗口 (Vue 3)
│   ├── options/           # 设置页面 (Vue 3)
│   ├── assets/            # 样式文件
│   └── utils/             # 工具函数
├── public/                # 静态资源
│   ├── manifest.json      # Manifest V3 配置
│   └── assets/            # 图标文件
├── dist/                  # 构建输出目录
└── package.json           # 项目配置
```

## 开发指南

### 安装依赖
```bash
npm install
# 或
pnpm install
```

### 开发模式
```bash
npm run dev
```

### 构建扩展
```bash
npm run build
```

构建完成后，`dist` 目录包含可以直接加载到 Chrome 的扩展文件。

### 加载扩展到 Chrome

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `dist` 目录

## 功能说明

### 基本功能
- **点击图标推送**: 点击工具栏图标推送剪贴板内容或当前页面 URL
- **右键菜单推送**: 选中文本后右键推送到指定设备
- **多设备支持**: 支持配置多个推送设备

### 设置选项
- **默认推送内容**: 选择点击图标时推送剪贴板还是当前页面 URL
- **自动复制**: 推送后是否自动复制到设备剪贴板
- **设备管理**: 添加、删除推送设备

## 与原版的兼容性

重写后的扩展完全兼容原版的功能：
- ✅ 支持所有原版功能
- ✅ 兼容原版的设置数据
- ✅ 保持相同的用户体验

## 技术细节

### Manifest V3 适配
- 使用 Service Worker 替代 Background Page
- 更新权限声明格式
- 适配新的 API 调用方式

### 现代化开发
- 使用 ES6+ 语法
- 模块化代码结构
- 现代化的构建流程
- 响应式设计

## 贡献指南

欢迎提交 Issue 和 Pull Request！

### 开发流程
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

与原项目保持一致的许可证。
