import type { Settings } from '@/types/index';

// Chrome Storage API 封装
export const storage = {
  // 获取存储数据
  async get<T = any>(keys: string | string[] | null): Promise<T> {
    return new Promise((resolve, reject) => {
      chrome.storage.local.get(keys, (result) => {
        if (chrome.runtime.lastError) {
          console.error('Storage get error:', chrome.runtime.lastError);
          reject(chrome.runtime.lastError);
        } else {
          console.log('Storage get result:', result);
          resolve(result as T);
        }
      });
    });
  },

  // 设置存储数据
  async set(data: Record<string, any>): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log('Storage set data:', data);
      chrome.storage.local.set(data, () => {
        if (chrome.runtime.lastError) {
          console.error('Storage set error:', chrome.runtime.lastError);
          reject(chrome.runtime.lastError);
        } else {
          console.log('Storage set successful');
          resolve();
        }
      });
    });
  },

  // 删除存储数据
  async remove(keys: string | string[]): Promise<void> {
    return new Promise((resolve, reject) => {
      chrome.storage.local.remove(keys, () => {
        if (chrome.runtime.lastError) {
          console.error('Storage remove error:', chrome.runtime.lastError);
          reject(chrome.runtime.lastError);
        } else {
          resolve();
        }
      });
    });
  },

  // 清空所有存储数据
  async clear(): Promise<void> {
    return new Promise((resolve, reject) => {
      chrome.storage.local.clear(() => {
        if (chrome.runtime.lastError) {
          console.error('Storage clear error:', chrome.runtime.lastError);
          reject(chrome.runtime.lastError);
        } else {
          resolve();
        }
      });
    });
  }
};

// 默认配置
export const defaultSettings: Settings = {
  server_urls: [],
  default_push_content: "clipboard",
  // Bark 推送参数配置
  push_params: {
    title: "Chrome 通知",
    subtitle: "",
    level: "active",
    volume: 5,
    badge: "",
    call: false,
    autoCopy: false,
    sound: "",
    icon: "",
    group: "Chrome 通知",
    ciphertext: ""
  }
};
