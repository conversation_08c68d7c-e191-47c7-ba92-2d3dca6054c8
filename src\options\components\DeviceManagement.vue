<template>
  <div class="space-y-6">
    <div>
      <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">设备管理</h3>
      <p class="text-gray-600 dark:text-gray-400 mb-6">添加和管理您的 Bark 推送设备。</p>

      <!-- 现有设备列表 -->
      <div v-if="Array.isArray(settings.server_urls) && settings.server_urls.length > 0" class="mb-6">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">已配置的设备</h4>
          <button
            @click="testAllDevices"
            :disabled="isTestingAll"
            class="btn-secondary text-sm"
          >
            <span v-if="!isTestingAll">测试全部设备</span>
            <span v-else>测试中...</span>
          </button>
        </div>
        <div class="space-y-3">
          <div
            v-for="(server, index) in settings.server_urls"
            :key="index"
            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
          >
            <div class="flex-1">
              <div class="font-medium text-gray-900 dark:text-gray-100">{{ server.server_name }}</div>
              <div class="text-sm text-gray-500 dark:text-gray-400 break-all">{{ server.server_url }}</div>
              <!-- 测试状态显示 -->
              <div v-if="testResults[index]" class="mt-2 flex items-center text-sm" :class="testResults[index].success ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                <svg v-if="testResults[index].success" class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <svg v-else class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                </svg>
                <span>{{ testResults[index].message }}</span>
              </div>
            </div>
            <div class="flex items-center space-x-2 ml-4">
              <!-- 测试按钮 -->
              <button
                @click="testDevice(index)"
                :disabled="testingStates[index]"
                class="p-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg transition-colors disabled:opacity-50"
                title="测试推送"
              >
                <svg v-if="!testingStates[index]" class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <svg v-else class="w-5 h-5 animate-spin" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 4V2A10 10 0 0 0 2 12h2a8 8 0 0 1 8-8z"/>
                </svg>
              </button>
              <!-- 删除按钮 -->
              <button
                @click="deleteDevice(index)"
                class="p-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg transition-colors"
                title="删除设备"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M7 21q-.825 0-1.412-.587T5 19V6H4V4h5V3h6v1h5v2h-1v13q0 .825-.587 1.413T17 21zM17 6H7v13h10zM9 17h2V8H9zm4 0h2V8h-2zM7 6v13z"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加新设备 -->
      <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
        <h4 class="text-md font-medium mb-4 text-gray-900 dark:text-gray-100">添加新设备</h4>

        <!-- 设备信息输入 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label for="server_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">设备别名</label>
            <input
              type="text"
              id="server_name"
              v-model="newDevice.name"
              placeholder="例如：我的iPhone"
              class="input-field"
            >
          </div>

          <div>
            <label for="server_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">推送地址</label>
            <input
              type="text"
              id="server_url"
              v-model="newDevice.url"
              placeholder="Bark Push URL: https://day.app/your_key/"
              class="input-field"
            >
          </div>
        </div>

        <button
          @click="addDevice"
          :disabled="!canAddDevice"
          class="btn-primary"
          :class="{ 'opacity-50 cursor-not-allowed': !canAddDevice }"
        >
          添加设备
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { Settings, NewDevice, TestResult } from '@/types/index'
import { PushService } from '@/utils/push'
import { validateURL } from '@/utils/index'

interface Props {
  settings: Settings
}

interface Emits {
  (e: 'update:settings', settings: Settings): void
  (e: 'show-message', message: string, type: 'success' | 'error'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const pushService = new PushService()

const newDevice = ref<NewDevice>({
  name: '',
  url: ''
})

// 测试相关的响应式数据
const testingStates = ref<Record<number, boolean>>({})
const testResults = ref<Record<number, TestResult>>({})
const isTestingAll = ref(false)

// 计算属性
const canAddDevice = computed(() => {
  return newDevice.value.name.trim() && newDevice.value.url.trim()
})

// 添加设备
const addDevice = () => {
  if (!canAddDevice.value) return

  // 验证 URL
  if (!validateURL(newDevice.value.url)) {
    emit('show-message', '请输入有效的 URL', 'error')
    return
  }

  const newServer = {
    server_name: newDevice.value.name.trim(),
    server_url: newDevice.value.url.trim()
  }

  const updatedSettings = { ...props.settings }
  if (!Array.isArray(updatedSettings.server_urls)) {
    updatedSettings.server_urls = []
  }

  updatedSettings.server_urls.push(newServer)
  emit('update:settings', updatedSettings)

  // 清空表单
  newDevice.value.name = ''
  newDevice.value.url = ''

  emit('show-message', '设备添加成功', 'success')
}

// 删除设备
const deleteDevice = (index: number) => {
  if (confirm('确定要删除这个设备吗？')) {
    const updatedSettings = { ...props.settings }
    if (!Array.isArray(updatedSettings.server_urls)) {
      updatedSettings.server_urls = []
      return
    }

    updatedSettings.server_urls.splice(index, 1)
    emit('update:settings', updatedSettings)
    emit('show-message', '设备已删除', 'success')
  }
}

// 测试设备推送功能
const testDevice = async (index: number) => {
  const server = props.settings.server_urls[index]
  if (!server) return

  // 设置测试状态
  testingStates.value[index] = true
  testResults.value[index] = { success: false, message: '' }

  try {
    const testMessage = `测试消息 - ${new Date().toLocaleTimeString()}`
    const serverUrl = server.server_url

    await pushService.sendMessage(testMessage, { serverUrl })

    // 测试成功
    testResults.value[index] = {
      success: true,
      message: '测试成功！请检查设备是否收到通知'
    }

  } catch (error: any) {
    // 测试失败
    testResults.value[index] = {
      success: false,
      message: `测试失败：${error.message || '网络错误或设备配置有误'}`
    }
  } finally {
    // 清除测试状态
    testingStates.value[index] = false

    // 5秒后清除测试结果
    setTimeout(() => {
      if (testResults.value[index]) {
        delete testResults.value[index]
      }
    }, 5000)
  }
}

// 测试全部设备
const testAllDevices = async () => {
  if (!Array.isArray(props.settings.server_urls) || props.settings.server_urls.length === 0) {
    emit('show-message', '没有可测试的设备', 'error')
    return
  }

  isTestingAll.value = true

  try {
    // 并行测试所有设备
    const testPromises = props.settings.server_urls.map((_, index) => testDevice(index))
    await Promise.allSettled(testPromises)

    // 统计测试结果
    const successCount = Object.values(testResults.value).filter(result => result?.success).length
    const totalCount = props.settings.server_urls.length

    if (successCount === totalCount) {
      emit('show-message', `全部 ${totalCount} 个设备测试成功`, 'success')
    } else {
      emit('show-message', `${successCount}/${totalCount} 个设备测试成功`, 'error')
    }
  } finally {
    isTestingAll.value = false
  }
}
</script>
