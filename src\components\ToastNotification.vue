<template>
  <!-- Toast 通知容器 -->
  <div class="fixed top-4 right-4 z-50 space-y-2">
    <div
      v-for="toast in toasts"
      :key="toast.id"
      class="toast-container transform transition-all duration-300 ease-in-out"
      :class="toast.visible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'"
    >
      <div
        class="flex items-center p-4 rounded-lg shadow-lg border max-w-sm"
        :class="getToastClass(toast.type)"
      >
        <!-- 图标 -->
        <div class="flex-shrink-0 mr-3">
          <svg
            v-if="toast.type === 'success'"
            class="w-5 h-5 text-green-600 dark:text-green-400"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          <svg
            v-else
            class="w-5 h-5 text-red-600 dark:text-red-400"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
          </svg>
        </div>
        
        <!-- 消息内容 -->
        <div class="flex-1 text-sm font-medium">
          {{ toast.message }}
        </div>
        
        <!-- 关闭按钮 -->
        <button
          @click="removeToast(toast.id)"
          class="flex-shrink-0 ml-3 p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import type { ToastMessage } from '@/types/index'

interface Emits {
  (e: 'toast-removed', id: number): void
}

const emit = defineEmits<Emits>()

const toasts = ref<ToastMessage[]>([])
let toastIdCounter = 0

// 显示 Toast
const showToast = (message: string, type: 'success' | 'error' = 'success') => {
  const id = ++toastIdCounter
  const toast: ToastMessage = {
    id,
    message,
    type,
    visible: false
  }

  toasts.value.push(toast)

  // 延迟显示动画
  nextTick(() => {
    toast.visible = true
  })

  // 自动移除
  setTimeout(() => {
    removeToast(id)
  }, 4000)
}

// 移除 Toast
const removeToast = (id: number) => {
  const index = toasts.value.findIndex(toast => toast.id === id)
  if (index > -1) {
    toasts.value[index].visible = false
    // 等待动画完成后移除
    setTimeout(() => {
      const currentIndex = toasts.value.findIndex(toast => toast.id === id)
      if (currentIndex > -1) {
        toasts.value.splice(currentIndex, 1)
        emit('toast-removed', id)
      }
    }, 300)
  }
}

// 获取 Toast 样式类
const getToastClass = (type: 'success' | 'error') => {
  return type === 'success'
    ? 'bg-green-50 dark:bg-green-900/30 text-green-800 dark:text-green-300 border-green-200 dark:border-green-700'
    : 'bg-red-50 dark:bg-red-900/30 text-red-800 dark:text-red-300 border-red-200 dark:border-red-700'
}

// 暴露方法给父组件
defineExpose({
  showToast
})
</script>

<style scoped>
.toast-container {
  backdrop-filter: blur(8px);
}

/* 确保 toast 在所有内容之上 */
.toast-container > div {
  backdrop-filter: blur(8px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 深色模式下的阴影调整 */
@media (prefers-color-scheme: dark) {
  .toast-container > div {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  }
}

/* 动画优化 */
.toast-container {
  will-change: transform, opacity;
}
</style>
