// Chrome Extension 相关类型定义

export interface ServerConfig {
  server_name: string;
  server_url: string;
}

export interface PushParams {
  title?: string;
  subtitle?: string;
  level?: 'active' | 'critical' | 'timeSensitive' | 'passive';
  volume?: number;
  badge?: number | string;
  call?: boolean;
  autoCopy?: boolean;
  sound?: string;
  icon?: string;
  group?: string;
  ciphertext?: string;
}

export interface Settings {
  server_urls: ServerConfig[];
  default_push_content: 'clipboard' | 'URL';
  push_params: PushParams;
}

export interface ToastMessage {
  id: number;
  message: string;
  type: 'success' | 'error';
  visible: boolean;
}

export interface TestResult {
  success: boolean;
  message: string;
}

// Chrome Extension 消息类型
export interface ChromeMessage {
  action: 'pushClipboard' | 'pushUrl' | 'pushText';
  url?: string;
  text?: string;
}

export interface ChromeMessageResponse {
  success: boolean;
  error?: string;
}

// Tab 配置
export interface TabConfig {
  id: string;
  name: string;
}

// 新设备表单
export interface NewDevice {
  name: string;
  url: string;
}

// 推送服务选项
export interface PushOptions {
  serverUrl?: string;
  params?: PushParams;
}
